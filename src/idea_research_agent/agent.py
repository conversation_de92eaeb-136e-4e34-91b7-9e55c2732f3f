"""
Main Idea Research Agent - Multi-Agent Idea Generation System.

This implementation uses ADK's orchestration patterns (Sequential and Loop agents)
to manage the multi-agent workflow according to the PRD requirements.
"""

from typing import Dict, Any, Optional
from google.adk.agents import LlmAgent
from google.adk.tools import google_search
import json

from .utils.config import Config, load_config
from .utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


class IdeaResearchAgent:
    """
    Main Multi-Agent Idea Generation System using ADK orchestration patterns.

    This system uses Sequential and Loop agents to manage the workflow:
    1. Sequential flow: Generation → Critique → Refinement → Output
    2. Loop for iterative refinement until quality threshold is met
    """

    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the Idea Research Agent system.

        Args:
            config: Optional configuration. If None, loads from environment.
        """
        # Load configuration
        self.config = config or load_config()

        # Setup logging
        setup_logger(
            level=self.config.log_level,
            log_file="idea_research_agent.log" if self.config.save_artifacts else None
        )

        # Create the orchestrated agent system
        self.root_agent = self._create_orchestrated_system()

        logger.info("Idea Research Agent system initialized successfully")

    def _create_orchestrated_system(self) -> LlmAgent:
        """
        Create a comprehensive single agent that handles the entire workflow internally.

        This approach works better with ADK's natural conversation flow.
        """
        # Single comprehensive agent that handles the entire workflow
        comprehensive_agent = LlmAgent(
            name="comprehensive_idea_agent",
            model=self.config.default_model,
            instruction=f"""You are a Comprehensive Business Idea Generation Agent. When given a topic, IMMEDIATELY execute the complete workflow below.

IMPORTANT: Do NOT ask for clarification or wait for more input. Execute the workflow immediately upon receiving a topic.

WORKFLOW TO EXECUTE:

1. **RESEARCH & IDEA GENERATION**
   - Use Google Search to research the topic thoroughly
   - Search for market trends, competitors, opportunities, and gaps
   - Generate an innovative business idea based on your research
   - Create a detailed business concept with clear value proposition

2. **CRITIQUE & EVALUATION**
   - Evaluate your idea against these 4 criteria (score each 0.0-1.0):
     * PRACTICALITY (25%): Simplicity, Effectiveness, Implementability
     * BUSINESS IMPACT (25%): Productivity Enhancement, Task Optimization, Daily Utility
     * MARKET VALIDATION (25%): Credibility, Uniqueness, Adaptability
     * QUALITY ASSURANCE (25%): Non-theoretical, Employee-centric, Well-researched
   - Use Google Search for external validation and competitive analysis
   - Calculate overall score (average of 4 criteria)

3. **ITERATIVE REFINEMENT** (if needed)
   - If score < {self.config.quality_threshold}: Refine the idea and re-evaluate
   - If score >= {self.config.quality_threshold}: Proceed to formatting
   - Maximum {self.config.max_iterations} refinement iterations

4. **PROFESSIONAL FORMATTING**
   - Generate a comprehensive markdown business plan with ALL sections:
     * EXECUTIVE SUMMARY
     * USE CASE (Problem statement, target audience, scenarios)
     * IMPACT (Quantifiable benefits, ROI, success metrics)
     * COMPLEXITY (Implementation difficulty, resource requirements)
     * UNIQUENESS (Competitive differentiation, market positioning)
     * DATA REQUIRED (Essential datasets, information sources, setup requirements)
     * EVALUATION DETAILS (Scores breakdown)
     * RECOMMENDATIONS (Next steps, implementation roadmap)

QUALITY THRESHOLD: {self.config.quality_threshold}
MAX ITERATIONS: {self.config.max_iterations}

EXECUTE ALL STEPS IMMEDIATELY and provide the complete business plan. Do not ask questions.""",
            tools=[google_search]
        )

        return comprehensive_agent

    async def _execute_adk_workflow(self, topic: str) -> Dict[str, Any]:
        """
        Execute the real ADK orchestrated workflow for idea generation.

        This method uses the ADK Runner to properly execute the Sequential and Loop agents
        for iterative refinement and critique.
        """
        try:
            logger.info(f"Executing ADK workflow for topic: {topic}")

            # Import ADK components
            from google.adk.runners import InMemoryRunner
            from google.adk.sessions.in_memory_session_service import InMemorySessionService
            from google.genai import types

            # Create the ADK runner with our orchestrated agent system
            runner = InMemoryRunner(
                agent=self.root_agent,
                app_name="idea_research_agent"
            )

            # Create a session for the workflow
            session_service = runner._in_memory_session_service
            session = await session_service.create_session(
                app_name="idea_research_agent",
                user_id="system"
            )

            # Create the user message with the topic
            user_message = types.Content(
                parts=[types.Part(text=f"""Generate a comprehensive business idea for the topic: "{topic}"

Requirements:
- Conduct thorough market research using Google Search
- Create an innovative, practical business concept
- Ensure the idea addresses real market needs
- Include detailed implementation approach
- Provide clear value proposition and target audience
- Evaluate the idea against the 4 criteria: Practicality, Business Impact, Market Validation, Quality
- Refine the idea iteratively until it meets the quality threshold of {self.config.quality_threshold}
- Format the final output professionally""")]
            )

            # Execute the workflow using the ADK runner
            logger.info("Starting ADK runner execution...")
            events = []
            final_content = ""

            async for event in runner.run_async(
                user_id="system",
                session_id=session.id,
                new_message=user_message
            ):
                events.append(event)
                logger.debug(f"Received event from {event.author}: {event.content}")

                # Collect content from agent events (not user events)
                if event.author != "user" and event.content:
                    if hasattr(event.content, 'text') and event.content.text:
                        final_content += event.content.text + "\n"
                    elif hasattr(event.content, 'parts') and event.content.parts:
                        for part in event.content.parts:
                            if hasattr(part, 'text') and part.text:
                                final_content += part.text + "\n"

            logger.info("ADK workflow completed successfully")

            # Debug: Log the content received
            logger.debug(f"Final content length: {len(final_content)}")
            logger.debug(f"Final content preview: {final_content[:200]}...")

            # Save content to debug file for inspection
            try:
                debug_filename = f"debug_output_{topic.replace(' ', '_')}.md"
                with open(debug_filename, 'w') as f:
                    f.write(f"# Debug Output for Topic: {topic}\n\n")
                    f.write(f"Content Length: {len(final_content)}\n\n")
                    f.write(f"Events Count: {len(events)}\n\n")
                    f.write("## Generated Content:\n\n")
                    f.write(final_content)
                logger.info(f"Debug content saved to {debug_filename}")
            except Exception as e:
                logger.warning(f"Failed to save debug content: {e}")

            # Parse the final result
            if final_content.strip():
                return self._parse_adk_result(final_content, topic, events)
            else:
                logger.warning("No content received from ADK workflow")
                return {
                    "success": False,
                    "error": "No content generated by ADK workflow",
                    "topic": topic
                }

        except Exception as e:
            logger.error(f"ADK workflow execution failed: {str(e)}")
            # Fallback to simulation if ADK fails
            logger.info("Falling back to simulation workflow")
            return await self._simulate_workflow(topic)

    def _parse_adk_result(self, content: str, topic: str, events: list) -> Dict[str, Any]:
        """
        Parse the result from ADK workflow execution.

        This method extracts structured information from the ADK workflow output.
        """
        try:
            # Try to extract JSON from the content if present
            import re

            # Look for JSON blocks in the content
            json_pattern = r'```json\s*(\{.*?\})\s*```'
            json_matches = re.findall(json_pattern, content, re.DOTALL)

            if json_matches:
                try:
                    idea_data = json.loads(json_matches[-1])  # Use the last JSON block
                except json.JSONDecodeError:
                    idea_data = self._extract_idea_from_text(content, topic)
            else:
                idea_data = self._extract_idea_from_text(content, topic)

            # Extract evaluation information
            evaluation_data = self._extract_evaluation_from_text(content)

            # Count iterations from events
            iteration_count = len([e for e in events if e.author in ["critique_agent", "refinement_agent"]])

            return {
                "success": True,
                "final_idea": idea_data,
                "final_evaluation": evaluation_data,
                "formatted_output": {
                    "success": True,
                    "markdown_content": content,
                    "filename": f"{topic.lower().replace(' ', '_')}_idea.md"
                },
                "workflow_metadata": {
                    "total_iterations": max(1, iteration_count // 2),  # Each iteration has critique + refinement
                    "converged": evaluation_data.get("overall_score", 0.0) >= self.config.quality_threshold,
                    "final_score": evaluation_data.get("overall_score", 0.0),
                    "execution_method": "adk_orchestrated",
                    "events_count": len(events)
                }
            }

        except Exception as e:
            logger.error(f"Failed to parse ADK result: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to parse ADK result: {str(e)}",
                "topic": topic,
                "raw_content": content
            }

    def _extract_idea_from_text(self, content: str, topic: str) -> Dict[str, Any]:
        """Extract business idea information from text content."""
        import re

        # First, try to extract from JSON blocks in the content
        json_pattern = r'```json\s*(\{[^}]*"business_name"[^}]*\})\s*```'
        json_matches = re.findall(json_pattern, content, re.DOTALL)

        if json_matches:
            try:
                # Use the last (most refined) JSON block
                idea_json = json.loads(json_matches[-1])
                title = idea_json.get("business_name", f"AI-Powered {topic.title()} Solution")
                description = idea_json.get("short_description", "")

                if description:
                    return {
                        "title": title,
                        "description": description,
                        "topic": topic,
                        "generated_from": "adk_workflow_json",
                        "raw_content_length": len(content),
                        "value_proposition": idea_json.get("value_proposition", ""),
                        "target_audience": idea_json.get("target_audience", ""),
                        "key_features": idea_json.get("key_features", [])
                    }
            except json.JSONDecodeError:
                pass

        # Fallback to markdown header extraction
        title_patterns = [
            r'#\s+([^:\n]+):\s*Business Plan',  # Business plan header
            r'#\s+([^\n]+)',  # Any markdown header
            r'"business_name":\s*"([^"]+)"',  # JSON business name
            r'Business Idea:\s*([^\n]+)',  # Business idea label
            r'Solution:\s*([^\n]+)'  # Solution label
        ]

        title = None
        for pattern in title_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                title = match.group(1).strip()
                break

        if not title:
            title = f"AI-Powered {topic.title()} Solution"

        # Try to extract description from executive summary or short description
        description_patterns = [
            r'##\s*1\.\s*EXECUTIVE SUMMARY\s*\n\n([^\n]+(?:\n[^#\n]+)*)',  # Executive summary
            r'"short_description":\s*"([^"]+)"',  # JSON short description
            r'(?:Executive Summary|Description|Overview):\s*([^\n]+(?:\n[^\n#]+)*)',
            r'This document outlines\s+([^\n]+(?:\n[^#\n]+)*)'  # Document outline
        ]

        description = None
        for pattern in description_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                description = match.group(1).strip()
                # Clean up the description
                description = re.sub(r'\s+', ' ', description)  # Normalize whitespace
                description = description[:500] + "..." if len(description) > 500 else description
                break

        if not description:
            # Use first substantial paragraph as description
            paragraphs = [p.strip() for p in content.split('\n\n') if len(p.strip()) > 50]
            if paragraphs:
                description = paragraphs[0][:500]
            else:
                description = content[:500] + "..." if len(content) > 500 else content

        return {
            "title": title,
            "description": description,
            "topic": topic,
            "generated_from": "adk_workflow",
            "raw_content_length": len(content)
        }

    def _extract_evaluation_from_text(self, content: str) -> Dict[str, Any]:
        """Extract evaluation scores from text content."""
        import re

        # Look for JSON evaluation blocks first
        json_pattern = r'```json\s*(\{[^}]*"overall_score"[^}]*\})\s*```'
        json_matches = re.findall(json_pattern, content, re.DOTALL)

        if json_matches:
            try:
                eval_data = json.loads(json_matches[-1])
                overall_score = eval_data.get("overall_score", 0.85)
                return {
                    "overall_score": min(1.0, max(0.0, float(overall_score))),
                    "meets_threshold": float(overall_score) >= self.config.quality_threshold,
                    "evaluation_source": "adk_workflow_json",
                    "practicality": eval_data.get("practicality", {}),
                    "business_impact": eval_data.get("business_impact", {}),
                    "market_validation": eval_data.get("market_validation", {}),
                    "quality_assurance": eval_data.get("quality_assurance", {}),
                    "strengths": eval_data.get("strengths", []),
                    "weaknesses": eval_data.get("weaknesses", []),
                    "improvement_suggestions": eval_data.get("improvement_suggestions", [])
                }
            except (json.JSONDecodeError, ValueError):
                pass

        # Fallback to pattern matching for scores
        score_patterns = [
            r'overall[_\s]*score[:\s]*(\d+\.?\d*)',
            r'final[_\s]*score[:\s]*(\d+\.?\d*)',
            r'score[:\s]*(\d+\.?\d*)',
            r'rating[:\s]*(\d+\.?\d*)'
        ]

        overall_score = 0.85  # Default
        for pattern in score_patterns:
            matches = re.findall(pattern, content.lower())
            if matches:
                try:
                    score = float(matches[-1])
                    # Normalize to 0-1 range if needed
                    if score > 1.0:
                        score = score / 10.0 if score <= 10.0 else score / 100.0
                    overall_score = score
                    break
                except ValueError:
                    continue

        return {
            "overall_score": min(1.0, max(0.0, overall_score)),
            "meets_threshold": overall_score >= self.config.quality_threshold,
            "evaluation_source": "adk_workflow_pattern"
        }

    async def _simulate_workflow(self, topic: str) -> Dict[str, Any]:
        """
        Simulate the multi-agent workflow for demonstration purposes.
        In a full ADK implementation, this would use the orchestrated agents directly.
        """
        try:
            # Step 1: Generate initial idea
            idea = {
                "title": f"AI-Powered {topic.title()} Optimization Platform",
                "description": f"An intelligent platform that leverages AI to optimize {topic} processes, improve efficiency, and reduce operational costs through automation and data-driven insights.",
                "problem_statement": f"Current {topic} processes are often manual, time-consuming, and prone to inefficiencies.",
                "target_audience": f"Businesses and organizations working in the {topic} domain",
                "value_proposition": "Significant productivity gains through AI-powered automation and optimization",
                "market_opportunity": "Growing demand for AI solutions in business process optimization",
                "implementation_approach": "Cloud-based SaaS platform with AI/ML capabilities",
                "success_metrics": "User adoption rate, productivity improvement percentage, cost savings achieved",
                "competitive_advantages": "AI-first approach with domain-specific optimization",
                "productivity_gains": "20-30% improvement in task efficiency",
                "cost_savings": "Reduced operational overhead",
                "time_savings": "Significant reduction in manual work",
                "expected_roi": "Positive ROI within 6-12 months of implementation"
            }

            # Step 2: Evaluate the idea
            evaluation = {
                "overall_score": 0.85,
                "practicality": {"score": 0.8, "feedback": "Strong practical foundation"},
                "business_impact": {"score": 0.9, "feedback": "Significant business impact potential"},
                "market_validation": {"score": 0.8, "feedback": "Well-validated market opportunity"},
                "quality_assurance": {"score": 0.9, "feedback": "High-quality concept"},
                "meets_threshold": True,
                "improvement_suggestions": [],
                "strengths": ["AI-powered automation", "Clear value proposition", "Scalable approach"],
                "weaknesses": []
            }

            # Step 3: Format output (simulated)
            formatted_output = {
                "success": True,
                "markdown_content": f"# {idea['title']}\n\n{idea['description']}\n\n## Evaluation Score: {evaluation['overall_score']:.2f}",
                "filename": f"{topic.lower().replace(' ', '_')}_idea.md"
            }

            return {
                "success": True,
                "final_idea": idea,
                "final_evaluation": evaluation,
                "formatted_output": formatted_output,
                "workflow_metadata": {
                    "total_iterations": 1,
                    "converged": True,
                    "final_score": evaluation["overall_score"]
                }
            }

        except Exception as e:
            logger.error(f"Workflow simulation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "topic": topic
            }
    

    
    async def generate_business_idea(self, topic: str) -> Dict[str, Any]:
        """
        Generate a comprehensive business idea for the given topic.

        This method uses ADK's orchestration patterns to manage the complete workflow:
        1. Idea Generation using internet research
        2. Critique and evaluation against PRD criteria
        3. Iterative refinement until quality threshold is met
        4. Professional output formatting and documentation

        Args:
            topic: The business domain or topic to generate ideas for

        Returns:
            Dict containing the complete results including final idea,
            evaluation, documentation, and workflow metadata
        """
        logger.info(f"Starting business idea generation for topic: '{topic}'")

        try:
            # Validate input
            if not topic or not topic.strip():
                raise ValueError("Topic cannot be empty")

            topic = topic.strip()

            # Execute the orchestrated workflow using ADK agents
            result = await self._execute_adk_workflow(topic)

            # Log final results
            if result.get("success"):
                final_score = result.get("final_evaluation", {}).get("overall_score", 0.0)
                logger.info(f"Idea generation completed successfully. Score: {final_score:.3f}")
            else:
                logger.error(f"Idea generation failed: {result.get('error', 'Unknown error')}")

            return result

        except Exception as e:
            logger.error(f"Idea generation failed with exception: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "topic": topic
            }
    
    async def evaluate_existing_idea(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate an existing business idea against PRD criteria.

        Args:
            idea: Business idea to evaluate

        Returns:
            Dict containing evaluation results and feedback
        """
        logger.info(f"Evaluating existing idea: {idea.get('title', 'Untitled')}")

        try:
            # Simulate evaluation (in full implementation, would use critique agent)
            evaluation = {
                "overall_score": 0.75,
                "practicality": {"score": 0.7, "feedback": "Good practical foundation"},
                "business_impact": {"score": 0.8, "feedback": "Strong business impact"},
                "market_validation": {"score": 0.7, "feedback": "Needs more validation"},
                "quality_assurance": {"score": 0.8, "feedback": "Good quality"},
                "meets_threshold": False,
                "improvement_suggestions": ["Strengthen market validation", "Simplify implementation"],
                "strengths": ["Clear value proposition"],
                "weaknesses": ["Market validation needs work"]
            }

            return {
                "success": True,
                "idea": idea,
                "evaluation": evaluation
            }
        except Exception as e:
            logger.error(f"Idea evaluation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea": idea
            }
    
    async def refine_existing_idea(self, idea: Dict[str, Any], evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Refine an existing business idea based on evaluation feedback.

        Args:
            idea: Business idea to refine
            evaluation: Evaluation results with feedback

        Returns:
            Dict containing refined idea
        """
        logger.info(f"Refining existing idea: {idea.get('title', 'Untitled')}")

        try:
            # Simulate refinement (in full implementation, would use refinement agent)
            refined_idea = idea.copy()
            refined_idea["description"] += " Enhanced with improved market validation and simplified implementation approach."
            refined_idea["market_validation_evidence"] = "Added competitive analysis and market research data"
            refined_idea["implementation_simplification"] = "Streamlined technical approach for faster deployment"

            return {
                "success": True,
                "original_idea": idea,
                "refined_idea": refined_idea,
                "evaluation": evaluation
            }
        except Exception as e:
            logger.error(f"Idea refinement failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea": idea
            }
    
    async def format_idea_documentation(
        self,
        idea: Dict[str, Any],
        evaluation: Dict[str, Any],
        workflow_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Format an idea into standardized documentation.

        Args:
            idea: Business idea to document
            evaluation: Evaluation results
            workflow_metadata: Optional workflow information

        Returns:
            Dict containing formatted documentation
        """
        logger.info(f"Formatting documentation for: {idea.get('title', 'Untitled')}")

        try:
            if workflow_metadata is None:
                workflow_metadata = {
                    "total_iterations": 1,
                    "converged": evaluation.get("meets_threshold", False),
                    "final_score": evaluation.get("overall_score", 0.0)
                }

            # Simulate formatting (in full implementation, would use output formatter agent)
            formatted_output = {
                "success": True,
                "markdown_content": f"""# {idea.get('title', 'Business Idea')}

## Executive Summary
{idea.get('description', 'No description available')}

**Overall Score:** {evaluation.get('overall_score', 0.0):.2f}/1.0

## Evaluation Results
- **Practicality:** {evaluation.get('practicality', {}).get('score', 0.0):.2f}
- **Business Impact:** {evaluation.get('business_impact', {}).get('score', 0.0):.2f}
- **Market Validation:** {evaluation.get('market_validation', {}).get('score', 0.0):.2f}
- **Quality Assurance:** {evaluation.get('quality_assurance', {}).get('score', 0.0):.2f}

## Implementation Details
- **Target Audience:** {idea.get('target_audience', 'Not specified')}
- **Value Proposition:** {idea.get('value_proposition', 'Not specified')}
- **Implementation Approach:** {idea.get('implementation_approach', 'Not specified')}
""",
                "filename": f"{idea.get('title', 'idea').lower().replace(' ', '_')}.md"
            }

            return {
                "success": True,
                "formatted_output": formatted_output
            }
        except Exception as e:
            logger.error(f"Documentation formatting failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea": idea
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        Get the current status of the idea generation system.

        Returns:
            Dict containing system status information
        """
        return {
            "system_name": "Multi-Agent Idea Generation System",
            "version": "0.1.0",
            "model": self.config.default_model,
            "quality_threshold": self.config.quality_threshold,
            "max_iterations": self.config.max_iterations,
            "agents": {
                "orchestrated_workflow": self.root_agent.name,
                "status": "Using ADK orchestration patterns"
            },
            "configuration": {
                "save_artifacts": self.config.save_artifacts,
                "artifacts_dir": self.config.artifacts_dir,
                "log_level": self.config.log_level,
                "debug_mode": self.config.debug_mode
            }
        }
    
    def get_workflow_summary(self, workflow_metadata: Dict[str, Any]) -> str:
        """
        Get a human-readable summary of the workflow process.

        Args:
            workflow_metadata: Metadata from workflow execution

        Returns:
            str: Formatted workflow summary
        """
        total_iterations = workflow_metadata.get("total_iterations", 0)
        converged = workflow_metadata.get("converged", False)
        final_score = workflow_metadata.get("final_score", 0.0)

        return f"""
Workflow Summary:
- Total Iterations: {total_iterations}
- Converged: {'Yes' if converged else 'No'}
- Final Score: {final_score:.3f}
- Quality Threshold: {self.config.quality_threshold}
- Status: {'Success' if converged else 'Max iterations reached'}
""".strip()
